<template>
  <transition mode="out-in" name="fade">
    <AirdropTab />
  </transition>
</template>

<script setup>
import AirdropTab from "~/components/myassets/AirdropTab.vue"

definePageMeta({
  layout: "new-app",
});

useHead({
  title: "Midnight | SecondSwap",
});

useSeoMeta({
  title: `Midnight | SecondSwap`,
  twitterTitle: `Midnight | SecondSwap`,
  description: "Check your wallet’s eligibility for the Midnight $Night token airdrop. Claim through SecondSwap to unlock early trading access and $2S token rewards.",
  twitterDescription: "Check your wallet’s eligibility for the Midnight $Night token airdrop. Claim through SecondSwap to unlock early trading access and $2S token rewards.",
  twitterCard: "summary_large_image",
  ogImage: "https://cdn.prod.website-files.com/67f4cfd516eb1fc384404845/6890c7f4abae2b21df90589b_Midnight-Cover.png",
  twitterImage: "https://cdn.prod.website-files.com/67f4cfd516eb1fc384404845/6890c7f4abae2b21df90589b_Midnight-Cover.png",
  ogType: "website",
})
</script>

<style scoped></style>
