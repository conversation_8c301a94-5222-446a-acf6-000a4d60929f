<template>
	<button @click="{ copy }">
		<button @click="copy"
			class="flex items-center gap-2 px-2 h-[20px] text-sm border rounded-lg text-primary border-primary whitespace-nowrap flex-nowrap"><svg
				width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path
					d="M11.8132 5.63924C11.8132 5.49246 11.7844 5.34704 11.7283 5.21143C11.6721 5.07575 11.5894 4.95239 11.4856 4.84855C11.3818 4.74471 11.2584 4.66207 11.1227 4.60588C10.9871 4.54973 10.8417 4.521 10.6949 4.521H5.63973C5.34315 4.521 5.05875 4.63884 4.84904 4.84855C4.63933 5.05826 4.52149 5.34266 4.52148 5.63924V10.6944C4.52149 10.8412 4.55022 10.9866 4.60636 11.1222C4.66256 11.2579 4.7452 11.3813 4.84904 11.4851C4.95288 11.5889 5.07624 11.6716 5.21191 11.7278C5.34753 11.7839 5.49295 11.8127 5.63973 11.8127H10.6949C10.8417 11.8127 10.9871 11.7839 11.1227 11.7278C11.2584 11.6716 11.3818 11.5889 11.4856 11.4851C11.5894 11.3813 11.6721 11.2579 11.7283 11.1222C11.7844 10.9866 11.8132 10.8412 11.8132 10.6944V5.63924ZM12.6882 10.6944C12.6882 10.9561 12.6368 11.2154 12.5366 11.4572C12.4365 11.699 12.2893 11.9187 12.1042 12.1038C11.9192 12.2888 11.6995 12.436 11.4577 12.5361C11.2159 12.6363 10.9566 12.6877 10.6949 12.6877H5.63973C5.378 12.6877 5.11876 12.6363 4.87695 12.5361C4.63512 12.436 4.41548 12.2888 4.23039 12.1038C4.0453 11.9187 3.89818 11.699 3.79801 11.4572C3.69786 11.2154 3.64648 10.9561 3.64648 10.6944V5.63924C3.64649 5.1106 3.85658 4.60371 4.23039 4.2299C4.60419 3.85609 5.11109 3.646 5.63973 3.646H10.6949C10.9566 3.646 11.2159 3.69738 11.4577 3.79753C11.6995 3.8977 11.9192 4.04481 12.1042 4.2299C12.2893 4.41499 12.4365 4.63463 12.5366 4.87646C12.6368 5.11828 12.6882 5.37751 12.6882 5.63924V10.6944Z"
					fill="#7CD3F8" />
				<path
					d="M1.3125 2.91667C1.3125 2.03338 2.03338 1.3125 2.91667 1.3125H8.75C9.05073 1.3125 9.31206 1.39191 9.53385 1.5529C9.69313 1.66852 9.81542 1.81455 9.91496 1.96704L10.0072 2.12085L10.0272 2.16073C10.1149 2.36445 10.0355 2.60546 9.83748 2.71558C9.63949 2.82564 9.39288 2.76596 9.26611 2.58398L9.24276 2.54582L9.18123 2.44385C9.12202 2.35341 9.06947 2.2969 9.02002 2.26099C8.96357 2.22001 8.88676 2.1875 8.75 2.1875H2.91667C2.51662 2.1875 2.1875 2.51662 2.1875 2.91667V8.74886L2.19434 8.84513C2.20718 8.94024 2.23856 9.03226 2.28719 9.11572C2.35207 9.22705 2.44527 9.31908 2.55721 9.38289C2.7671 9.50257 2.83979 9.77 2.72013 9.9799C2.60045 10.1897 2.33357 10.263 2.1237 10.1434C1.87791 10.0033 1.67368 9.80053 1.53125 9.55607C1.38883 9.31163 1.3132 9.03404 1.3125 8.75114V2.91667Z"
					fill="#7CD3F8" />
			</svg>
			<span>{{ isCopied ? 'Copied' : 'Copy' }}</span></button>
	</button>
</template>

<script setup>
const props = defineProps({
	text: String,
})

const isCopied = ref(false);

const copy = async () => {
	try {
		await navigator.clipboard.writeText(props.text)
		isCopied.value = true;
		setTimeout(() => {
			isCopied.value = false;
		}, 2000);
	} catch {
		console.error()
	}
}
</script>
