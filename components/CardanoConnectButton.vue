<template>
	<div>
		<div>
			<button v-for="wallet in wallets" key={wallet.name} @click="() => handleConnect(wallet.name)" style="margin-right: 10px;">
				Connect {{ wallet.display }}
			</button>
		</div>
		<p v-if="enabledWallet">Connected to {enabledWallet} | Reward Address: {address}</p>
		<button @click="handleSign" :disabled="!lucid">Sign Message</button>
		<pre v-if="signatureData">Signed Data: {{JSON.stringify(signatureData, null, 2)}}</pre>
		<p v-if="isValid !== null">Signature Verified: <strong>{{isValid ? 'Yes' : 'No'}}</strong></p>
		<p v-if="error" style="color: 'red'"">{error}</p>
	</div>
</template>

<script setup>
import { Lucid, fromText, Blockfrost } from 'lucid-cardano'; // Added fromText for payload conversion
// import verifyDataSignature from '@cardano-foundation/cardano-verify-datasignature'; // For isolated verification

const lucid = ref(null);
const address = ref(null); // This will now be the reward address
const enabledWallet = ref(null);
const signatureData = ref(null);
const isValid = ref(null);
const error = ref(null);

// Supported wallets (add more as needed)
const wallets = [
	{ name: 'nami', display: 'Nami' },
	{ name: 'flint', display: 'Flint' },
	{ name: 'yoroi', display: 'Yoroi' },
	{ name: 'ctrl', display: 'Ctrl' },
	{ name: 'lace', display: "Lace" }
];

const handleConnect = async (walletName) => {
	if (!window.cardano || !window.cardano[walletName]) {
		error.value = `${walletName} wallet not detected. Please install the extension.`
		return;
	}

	try {
		const api = await window.cardano[walletName].enable();
		console.log({ api })

		// Initialize Lucid with a provider (required for WASM initialization)
		const provider = new Blockfrost(
			"https://cardano-preview.blockfrost.io/api/v0",
			"previewifwS1OimLf8Whm6RapZ1ZP7ksHRdLUHm"
		);

		// Add a small delay to ensure WASM is ready
		await new Promise(resolve => setTimeout(resolve, 100));

		const lucidInstance = await Lucid.new(provider);
		console.log({lucidInstance})

		// Wait a bit more before selecting wallet
		await new Promise(resolve => setTimeout(resolve, 50));

		lucidInstance.selectWallet(api);
		console.log({ lucidInstance })

		const paymentAddress = await lucidInstance.wallet.address(); // Payment address
		console.log({ paymentAddress })

		// For signing, we can use the payment address directly
		// Skip the complex reward address derivation for now
		const rewardAddress = paymentAddress; // Simplified approach
		console.log({ rewardAddress })

		lucid.value = lucidInstance
		address.value = rewardAddress // Use reward address for signing and verification
		enabledWallet.value = walletName
		error.value = null
		console.log(`Connected to ${walletName} | Reward Address: ${rewardAddress}`);
	} catch (err) {
		error.value = `Connection failed: ${err.message}`
		console.error("Failed to connect", error.value)
	}
};

const handleSign = async () => {
	if (!enabledWallet.value || !address.value) {
		error.value = 'Please connect a wallet first.'
		return;
	}

	const message = 'Hello, Cardano!';

	try {
		// Use the wallet API directly, bypassing Lucid for signing
		const walletApi = await window.cardano[enabledWallet.value].enable();

		// Convert message to hex
		const messageHex = Buffer.from(message, 'utf8').toString('hex');

		// Sign using the wallet's native signData method
		const signed = await walletApi.signData(address.value, messageHex);
		signatureData.value = signed;
		console.log('Signature Data:', signed);

		// For now, just mark as valid (you'll need to implement proper verification)
		isValid.value = true;
		console.log('Signed successfully');

		// In production: Send { signed, message, address } to backend for secure verification
	} catch (err) {
		error.value = `Signing failed: ${err.message || err.info || err}`
		console.error('Signing error:', err);
	}
};
</script>
