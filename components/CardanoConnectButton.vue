<template>
	<div>
		<div>
			<button v-for="wallet in wallets" key={wallet.name} @click="() => handleConnect(wallet.name)" style="margin-right: 10px;">
				Connect {{ wallet.display }}
			</button>
		</div>
		<p v-if="enabledWallet">Connected to {enabledWallet} | Reward Address: {address}</p>
		<button @click="handleSign" :disabled="!lucid">Sign Message</button>
		<pre v-if="signatureData">Signed Data: {{JSON.stringify(signatureData, null, 2)}}</pre>
		<p v-if="isValid !== null">Signature Verified: <strong>{{isValid ? 'Yes' : 'No'}}</strong></p>
		<p v-if="error" style="color: 'red'"">{error}</p>
	</div>
</template>

<script setup>
import { Lucid, fromText } from 'lucid-cardano'; // Added fromText for payload conversion
// import verifyDataSignature from '@cardano-foundation/cardano-verify-datasignature'; // For isolated verification

const lucid = ref(null);
const address = ref(null); // This will now be the reward address
const enabledWallet = ref(null);
const signatureData = ref(null);
const isValid = ref(null);
const error = ref(null);

// Supported wallets (add more as needed)
const wallets = [
	{ name: 'nami', display: 'Nami' },
	{ name: 'flint', display: 'Flint' },
	{ name: 'yoroi', display: 'Yoroi' },
	{ name: 'ctrl', display: 'Ctrl' },
	{ name: 'lace', display: "Lace" }
];

const handleConnect = async (walletName) => {
	if (!window.cardano || !window.cardano[walletName]) {
		error.value = `${walletName} wallet not detected. Please install the extension.`
		return;
	}

	try {
		const api = await window.cardano[walletName].enable();
		console.log({ api })
		const lucidInstance = await Lucid.new(); // No provider needed for signing
		console.log({lucidInstance})
		lucidInstance.selectWallet(api);
		console.log({ lucidInstance })
		const paymentAddress = await lucidInstance.wallet.address(); // Payment address
		console.log({ paymentAddress })
		// Derive reward (stake) address for signing
		const addressDetails = lucidInstance.utils.getAddressDetails(paymentAddress);
		console.log({ addressDetails })
		const stakeCredential = addressDetails.stakeCredential;
		console.log({ stakeCredential })
		if (!stakeCredential) {
			throw new Error('No stake credential found in address.');
		}
		const rewardAddress = lucidInstance.utils.credentialToRewardAddress(stakeCredential);
		console.log({ rewardAddress })

		lucid.value = lucidInstance
		address.value = rewardAddress // Use reward address for signing and verification
		enabledWallet.value = walletName
		error.value = null
		console.log(`Connected to ${walletName} | Reward Address: ${rewardAddress}`);
	} catch (err) {
		error.value = `Connection failed: ${err.message}`
		console.error("Failed to connect", error.value)
	}
};

const handleSign = async () => {
	if (!lucid.value || !address.value) {
		error.value = 'Please connect a wallet first.'
		return;
	}

	const message = 'Hello, Cardano!';
	const payload = fromText(message); // Convert string to Uint8Array (no hex needed)

	try {
		// Create message object and sign (replaces deprecated signData)
		const msg = lucid.newMessage(address.value, payload);
		const signed = await msg.sign();
		signatureData.value = signed
		console.log('Signature Data:', signed);

		// Verify the signature (isolated function – see below)
		const verified = verifySignature(signed.signature, signed.key, message, address.value);
		isValid.value = verified
		console.log('Verified:', verified);

		// In production: Send { signed, message, address } to backend for secure verification
	} catch (err) {
		error.value = `Signing failed: ${err.message || err.info}`
	}
};
</script>
