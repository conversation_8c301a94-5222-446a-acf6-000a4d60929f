<template>
  <Teleport to="body">
    <input type="checkbox" id="midnightSummaryModal" class="modal-toggle" />
    <div :class="[
      'md:translate-y-0',
    ]" role="dialog" class="modal bg-black/20 backdrop-blur-[12px] backdrop:bg-transparent font-medium">
      <div
        class="modal-box min-w-full md:min-w-[480px] md:max-w-[480px] pt-8 px-5 pb-6 bg-modal md:rounded-[12px] fixed bottom-0 md:static transform transition-all duration-500 ease-out">
        <div class="flex justify-end mb-8">
          <div class="modal-action">
            <label for="midnightSummaryModal"
              class="text-lg btn btn-xs btn-circle btn-ghost hover:bg-transparent focus:outline-none">✕</label>
          </div>
        </div>
        <div class="flex flex-col items-center">
          <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M70 37.2572V40.0172C69.9963 46.4864 67.9015 52.7812 64.028 57.9626C60.1545 63.1441 54.7098 66.9346 48.5061 68.7688C42.3023 70.6031 35.6718 70.3828 29.6034 68.1409C23.535 65.899 18.354 61.7555 14.8329 56.3284C11.3118 50.9013 9.63937 44.4813 10.065 38.0261C10.4907 31.5709 12.9916 25.4261 17.1948 20.5084C21.3981 15.5906 27.0784 12.1633 33.3886 10.7376C39.6988 9.3119 46.3008 9.96418 52.21 12.5972"
              stroke="#50F187" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M70 16L40 46L31 37.009" stroke="#50F187" stroke-width="3" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
          <div class="mt-8 text-xl font-medium text-center">
            <div>Claim <span class="text-success">{{ toBigNumber(amount).div(toBigNumber(1e6)).toFixed(6) }}
                NIGHT</span></div>
            <div>Submitted Successfully!</div>
          </div>
          <div class="mt-2 text-sm text-center text-white/50">Your NIGHT token claim has been submitted and is now
            being processed. You
            can view
            your claim status in
            the My Assets section of your dashboard.</div>

          <div class="w-full p-6 mt-8 bg-modal-textbox rounded-xl">
            <div class="flex items-center justify-between">
              <div>
                <div>Claim Summary</div>
                <div class="text-sm text-white/50">Keep this information safe for your records.</div>
              </div>
              <div class="flex items-center gap-2">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_8263_5332)">
                    <path
                      d="M11.334 2.22676C12.3395 2.80733 13.1759 3.64044 13.7605 4.64362C14.3451 5.64681 14.6576 6.78527 14.6669 7.94632C14.6763 9.10736 14.3823 10.2507 13.814 11.2632C13.2457 12.2757 12.4228 13.1222 11.4268 13.719C10.4308 14.3157 9.29623 14.642 8.13538 14.6655C6.97454 14.689 5.82768 14.4089 4.80835 13.8529C3.78902 13.297 2.93256 12.4844 2.32376 11.4958C1.71496 10.5071 1.37492 9.37656 1.33732 8.21609L1.33398 8.00009L1.33732 7.78409C1.37465 6.63275 1.70968 5.51073 2.30974 4.52742C2.90981 3.54411 3.75442 2.73306 4.76125 2.17335C5.76807 1.61363 6.90275 1.32436 8.05465 1.33372C9.20656 1.34308 10.3364 1.65076 11.334 2.22676ZM10.472 6.19543C10.3572 6.08064 10.2045 6.01169 10.0424 6.0015C9.88042 5.99131 9.72025 6.04059 9.59198 6.14009L9.52932 6.19543L7.33398 8.39009L6.47198 7.52876L6.40932 7.47342C6.28104 7.37399 6.1209 7.32477 5.95892 7.33499C5.79694 7.34521 5.64426 7.41417 5.52949 7.52893C5.41473 7.6437 5.34577 7.79638 5.33555 7.95836C5.32533 8.12034 5.37455 8.28048 5.47398 8.40876L5.52932 8.47142L6.86265 9.80476L6.92532 9.86009C7.04223 9.9508 7.18601 10 7.33398 10C7.48196 10 7.62574 9.9508 7.74265 9.86009L7.80532 9.80476L10.472 7.13809L10.5273 7.07542C10.6268 6.94715 10.6761 6.78699 10.6659 6.62497C10.6557 6.46295 10.5868 6.31022 10.472 6.19543Z"
                      fill="#50F187" />
                  </g>
                  <defs>
                    <clipPath id="clip0_8263_5332">
                      <rect width="16" height="16" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                <span class="text-sm text-success">Submitted</span>
              </div>
            </div>
            <div class="flex flex-col items-stretch gap-4 mt-8">
              <div class="p-4 rounded bg-modal">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-xs text-white/50">Wallet Eligibility</div>
                    <div class="max-w-[160px] lg:max-w-[280px] mt-2 overflow-hidden text-sm text-ellipsis">{{
                      walletEligibility }}</div>
                  </div>
                  <CopyButton :text="walletEligibility" />
                </div>
              </div>

              <div class="p-4 rounded bg-modal">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-xs text-white/50">Network</div>
                    <div class="mt-2 text-sm capitalize">{{ network }}</div>
                  </div>
                </div>
              </div>

              <div class="p-4 rounded bg-modal">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-xs text-white/50">Destination Address (Cardano Address)</div>
                    <div class="max-w-[160px] lg:max-w-[280px] mt-2 overflow-hidden text-sm text-ellipsis">{{
                      destinationAddress }}</div>
                  </div>
                  <CopyButton :text="destinationAddress" />
                </div>
              </div>

              <div class="p-4 rounded bg-modal">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-xs text-white/50">Submission Time</div>
                    <div class="mt-2 text-sm">{{ submissionTime }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <nuxt-link to="https://app.secondswap.io" class="w-full">
            <div class="w-full h-10 mt-6 btn btn-primary btn-sm">
              Go to My Assets
            </div>
          </nuxt-link>
        </div>
      </div>
    </div>
    <label class="modal-backdrop" for="midnightSummaryModal">Close</label>
  </Teleport>
</template>

<script setup>
const props = defineProps({
  amount: Number,
  walletEligibility: String,
  network: String,
  destinationAddress: String,
  submissionTime: String,
})
</script>

<style scoped></style>
