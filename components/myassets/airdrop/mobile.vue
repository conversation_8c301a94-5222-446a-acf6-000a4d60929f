<template>
	<div class="mb-10 md:mb-[72px]">
		<div class="flex flex-col gap-2">
			<!-- Midnight -->
			<div class="card-lot relative bg-white/[6%] rounded-xl group">
				<div class="relative collapse">
					<input @click.stop="toggleDropdown(0)" type="checkbox" class="w-12 h-10" :class="isOpen[1]
						? 'absolute w-1/5 top-16 right-0'
						: 'absolute w-1/5 bottom-0 right-0'
						" />
					<div class="collapse-title pl-4 p-5 h-[142px]">
						<div>
							<div class="flex items-center justify-between mb-4">
								<div class="flex items-center gap-4">
									<div class="relative w-[44px] h-[44px] flex-shrink-0">
										<img class="w-full h-full rounded-full coin-border" :src="nightTokenImg" alt="token-img" />

										<img class="absolute bottom-0 w-[16px] h-[16px]" :src="networkIconBySymbol[network]"
											alt="network-img" />
									</div>
									<div class="flex flex-col">
										<h5 class="text-white text-2sm">NIGHT</h5>
										<h5 class="text-sm text-input-icons">Midnight Network</h5>
									</div>
								</div>
								<div class="dropdown dropdown-end">
									<div tabindex="0" role="button" class="flex justify-center">
										<inline-svg class="w-1 h-5" :src="optionsIcon"></inline-svg>
									</div>
									<ul tabindex="0"
										class="dropdown-content text-2sm bg-base-100 py-3 overflow-y-auto overflow-x-hidden rounded-[8px] z-[1] w-[110px] border border-textbox-border shadow-[0_0_4px_0_#353333] text-white mr-2 -mt-0.5">
										<li class="transition-colors duration-200 cursor-pointer hover:bg-white/10">
											<a :href="useRuntimeConfig().public.midnightClaimPortalUrl" target="_blank"
												class="pl-3 h-[30px] flex items-center"> Claim </a>
										</li>
									</ul>
								</div>
							</div>
							<div class="text-white mb-1 text-[18px] leading-[22px]">
								<span v-if="nightEligibleAmount !== null">{{ nightEligibleAmount === "Error" ? "Failed to fetch" :
									`${nightEligibleAmount} NIGHT` }}</span>
								<span v-else class="flex justify-start">
									<span class="loading loading-spinner loading-xs"></span>
								</span>
							</div>
							<div>
								<div class="flex justify-between">
									<div class="text-sm text-input-icons">
										Claimable
										<span class="text-success">
											Pending
										</span>
									</div>
									<label for="0">
										<inline-svg width="14" :src="chevronDown" :style="{
											transform: isOpen[0]
												? 'rotate(180deg)'
												: 'rotate(0deg)',
											transition: 'transform 0.3s ease',
										}" />
									</label>
								</div>
							</div>
						</div>
						<!-- {{ token }} -->
					</div>
					<div class="text-sm collapse-content">
						<div class="flex flex-col gap-3">
							<div class="flex justify-between">
								<h5 class="text-input-icons">Plan Name</h5>
								<div class="text-white">
									Glacier Drop
								</div>
							</div>
							<div class="flex justify-between">
								<h5 class="text-input-icons">Sell Limit</h5>
								<div class="text-white">
									N/A
								</div>
							</div>
							<div class="flex justify-between">
								<h5 class="text-input-icons">Next Unlock</h5>
								<div class="text-white">
									Pending
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- SecondSwap -->
			<div class="card-lot relative bg-white/[6%] rounded-xl group">
				<div class="relative collapse">
					<input @click.stop="toggleDropdown(1)" type="checkbox" class="w-12 h-10" :class="isOpen[1]
						? 'absolute w-1/5 top-16 right-0'
						: 'absolute w-1/5 bottom-0 right-0'
						" />
					<div class="collapse-title pl-4 p-5 h-[142px]">
						<div>
							<div class="flex items-center justify-between mb-4">
								<div class="flex items-center gap-4">
									<div class="relative w-[44px] h-[44px] flex-shrink-0">
										<img class="w-full h-full rounded-full coin-border" :src="secondSwapTokenImg" alt="token-img" />

										<img class="absolute bottom-0 w-[16px] h-[16px]" :src="networkIconBySymbol[network]"
											alt="network-img" />
									</div>
									<div class="flex flex-col">
										<h5 class="text-white text-2sm">2SWAP</h5>
										<h5 class="text-sm text-input-icons">SecondSwap</h5>
									</div>
								</div>
								<div class="dropdown dropdown-end">
									<div tabindex="0" role="button" class="flex justify-center">
										<inline-svg class="w-1 h-5" :src="optionsIcon"></inline-svg>
									</div>
									<ul tabindex="0"
										class="dropdown-content text-2sm bg-base-100 py-3 overflow-y-auto overflow-x-hidden rounded-[8px] z-[1] w-[110px] border border-textbox-border shadow-[0_0_4px_0_#353333] text-white mr-2 -mt-0.5">
										<li class="transition-colors duration-200 cursor-pointer hover:bg-white/10">
											<a href="https://docs.secondswap.io/usd2s-token/token-utility" target="_blank"
												class="pl-3 h-[30px] flex items-center"> Learn More </a>
										</li>
									</ul>
								</div>
							</div>
							<div class="text-white mb-1 text-[18px] leading-[22px]">
								1 Entry
							</div>
							<div>
								<div class="flex justify-between">
									<div class="text-sm text-input-icons">
										Claimable
										<span class="text-success">
											Pending
										</span>
									</div>
									<label for="1">
										<inline-svg width="14" :src="chevronDown" :style="{
											transform: isOpen[1]
												? 'rotate(180deg)'
												: 'rotate(0deg)',
											transition: 'transform 0.3s ease',
										}" />
									</label>
								</div>
							</div>
						</div>
						<!-- {{ token }} -->
					</div>
					<div class="text-sm collapse-content">
						<div class="flex flex-col gap-3">
							<div class="flex justify-between">
								<h5 class="text-input-icons">Plan Name</h5>
								<div class="text-white">
									Airdrop
								</div>
							</div>
							<div class="flex justify-between">
								<h5 class="text-input-icons">Sell Limit</h5>
								<div class="text-white">
									N/A
								</div>
							</div>
							<div class="flex justify-between">
								<h5 class="text-input-icons">Next Unlock</h5>
								<div class="text-white">
									Pending
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import nightTokenImg from "assets/images_new/common/night-token.png"
import secondSwapTokenImg from "assets/images_new/common/2swap-token.png"
import InlineSvg from "vue-inline-svg";
import chevronDown from "~/assets/images_new/icons/chevron-down.svg";
import optionsIcon from "~/assets/images_new/icons/options.svg";

const newWeb3Store = useNewWeb3Store()
const { networkIconBySymbol } = newWeb3Store

const props = defineProps({
	network: {
		type: String,
	},
	nightEligibleAmount: {
		type: Number,
	}
});


const isOpen = ref([]);
const selectedIndex = ref(0);
let planOpen = ref(false);

function toggleDropdown(index) {
	isOpen.value[index] = !isOpen.value[index];
}

</script>
