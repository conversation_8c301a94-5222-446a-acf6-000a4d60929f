<template>
	<table class="w-full">
		<thead class="text-sm font-medium text-input-icons">
			<tr>
				<td class="w-[20%] py-5">Token</td>
				<td class="w-[15%]">Plan Name</td>
				<td class="w-[15%]">
					<div class="flex items-center">
						<h5>Amount</h5>
						<inline-svg :src="sortIcon" class="fill-input-icons"></inline-svg>
					</div>
				</td>
				<td class="w-[15%]">Claimable</td>
				<td class="w-[15%]">Next Unlock</td>
				<td class="w-[15%]">Sell Limit</td>
				<td class="w-[5%]"></td>
			</tr>
		</thead>
		<tbody class="text-2sm">
			<tr class="border-b border-textbox-border last:border-0 hover:bg-white hover:bg-opacity-[4%]">
				<td class="py-5">
					<div class="flex items-center gap-3">
						<div class="relative w-[44px] h-[44px] flex-shrink-0">
							<img class="w-full h-full rounded-full" :src="nightTokenImg" alt="token-img" />

							<img class="absolute bottom-0 w-[16px] h-[16px]" :src="networkIconBySymbol[network]" alt="network-img" />
						</div>
						<div>
							<h4>NIGHT</h4>
							<h5 class="text-sm text-input-icons">Midnight Network</h5>
						</div>
					</div>
				</td>
				<td class="text-primary">
					Glacier Drop
				</td>
				<td>
					<span v-if="nightEligibleAmount !== null">
						{{ nightEligibleAmount === "Error" ? "Failed to fetch" : `${nightEligibleAmount} NIGHT` }}
						<!-- {{
							formatToken(
								divideNumberUsingDecimals(plan.raw_amount, token.token_decimal)
							)
						}} {{ token.token_ticker }} -->
					</span>
					<span v-else class="flex justify-start">
						<span class="loading loading-spinner loading-xs"></span>
					</span>
				</td>

				<td class="text-[#FFB784]">
					Pending
				</td>
				<td class="text-[#FFB784]">
					Pending
				</td>
				<td>N/A</td>
				<td>
					<div class="flex items-center gap-2">
						<a :href="useRuntimeConfig().public.midnightClaimPortalUrl" target="_blank"><button type="button"
								class="btn btn-outline w-16 btn-xs border-primary text-primary !rounded-2sm">
								Claim
							</button>
						</a>
						<button type="button" class="btn w-16 btn-outline btn-xs border-primary text-primary !rounded-2sm" disabled>
							List
						</button>
					</div>
				</td>
			</tr>
			<tr class="border-b border-textbox-border last:border-0 hover:bg-white hover:bg-opacity-[4%]">
				<td class="py-5">
					<div class="flex items-center gap-3">
						<div class="relative w-[44px] h-[44px] flex-shrink-0">
							<img class="w-full h-full rounded-full" :src="secondSwapTokenImg" alt="token-img" />

							<img class="absolute bottom-0 w-[16px] h-[16px]" :src="networkIconBySymbol[network]" alt="network-img" />
						</div>
						<div>
							<h4>2SWAP</h4>
							<h5 class="text-sm text-input-icons">SecondSwap</h5>
						</div>
					</div>
				</td>
				<td class="text-primary">
					Airdrop
				</td>
				<td>
					<span>
						1 Entry
					</span>
				</td>

				<td class="text-[#FFB784]">
					Pending
				</td>
				<td class="text-[#FFB784]">
					Pending
				</td>
				<td>N/A</td>
				<td>
					<div class="flex items-center gap-2">
						<a target="_blank" href="https://docs.secondswap.io/usd2s-token/token-utility"
							class="btn w-full btn-outline btn-xs border-primary text-primary !rounded-2sm">
							Learn More
						</a>
					</div>
				</td>
			</tr>
		</tbody>
	</table>
</template>

<script setup>
import nightTokenImg from "assets/images_new/common/night-token.png"
import secondSwapTokenImg from "assets/images_new/common/2swap-token.png"

const newWeb3Store = useNewWeb3Store()
const { networkIconBySymbol } = newWeb3Store

const props = defineProps({
	network: {
		type: String,
	},
	nightEligibleAmount: {
		type: Number,
	}
});

</script>

<style lang="scss" scoped></style>
