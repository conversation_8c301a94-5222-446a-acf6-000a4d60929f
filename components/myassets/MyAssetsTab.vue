<template>
  <div>
    <!-- Desktop Table -->
    <div class="hidden lg:block mb-10 md:mb-[72px]">
      <TableMyAssets @showClaimEvent="$emit('showClaimEvent', $event.plan.vesting_address)"
        @showListEvent="$emit('showListEvent', $event.plan)" @showSummaryEvent="$emit('showSummaryEvent', $event.plan)"
        :tokenPlans="tokenPlans" :claimableAmounts="claimableAmounts" />
    </div>

    <!-- Mobile Cards -->
    <div class="lg:hidden mb-10 md:mb-[72px]">
      <div v-if="!tokenPlans || tokenPlans.length == 0">
        <NoData :redirectButton="true" content="No Asset Found"></NoData>
      </div>
      <div v-for="(token, index) in tokenPlans" :key="index" class="pb-2">
        <CardAsset @claimEvent="$emit('claimEvent', $event)" @listEvent="$emit('listEvent', $event)"
          @summaryEvent="$emit('summaryEvent', $event)" :token="token" :claimableAmounts="claimableAmounts"
          :index="index" />
      </div>
    </div>
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import lockIcon from "~/assets/images_new/icons/lock.svg";

const props = defineProps({
  tokenPlans: {
    type: Array,
    default: () => []
  },
  claimableAmounts: {
    type: Array,
    default: () => []
  },
  userSummary: {
    type: Object,
    default: () => ({})
  },
  claimableAmountSum: {
    type: [String, Number],
    default: 0
  },
  formatUSDT: {
    type: Function,
    required: true
  },
  toBigNumber: {
    type: Function,
    required: true
  }
});

const emit = defineEmits([
  'claimEvent',
  'listEvent',
  'summaryEvent',
  'showClaimEvent',
  'showListEvent',
  'showSummaryEvent'
]);
</script>

<style scoped>
/* Add any specific styles for the my assets tab */
</style>
