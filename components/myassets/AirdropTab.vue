<template>
	<div class="min-h-screen pt-20 md:p-6">
		<!-- Header -->
		<div class="mb-12 text-center">
			<h1 class="mb-4 text-white font-medium text-2xl leading-[30px] tracking-[0.02em]">NIGHT Token Claim</h1>
			<div class="flex items-center justify-center gap-2">
				<div class="flex items-center gap-2 px-3 py-1 bg-white rounded-full">
					<svg width="16" height="19" viewBox="0 0 16 19" fill="none" xmlns="http://www.w3.org/2000/svg">
						<g clip-path="url(#clip0_8329_9809)">
							<path
								d="M13.6592 5.58329L9.6431 1.65051L7.9995 0.0410156L6.35551 1.65091L2.3402 5.58289C-0.78034 8.63869 -0.779929 13.6113 2.3406 16.6666C5.46155 19.7228 10.5391 19.7228 13.6592 16.667C16.7802 13.6109 16.7802 8.63869 13.6592 5.58249V5.58329ZM12.5214 15.5532C10.0241 17.9987 5.97541 17.9987 3.47808 15.5532C0.980756 13.1077 0.980756 9.14304 3.47808 6.69756L7.16614 3.08604L7.99993 2.26955L8.8333 3.08565L12.5214 6.69716C15.0187 9.14268 15.0187 13.1073 12.5214 15.5528V15.5532Z"
								fill="#0000FE" />
							<path
								d="M13.1219 10.6305L13.0918 10.3033H8.66666V11.877H11.4401C11.2917 12.4877 10.977 13.0394 10.5164 13.4905C9.85902 14.1341 8.98334 14.4885 8.05124 14.4885C7.11862 14.4885 6.24295 14.1338 5.58558 13.49C4.22615 12.1588 4.22615 9.99241 5.58558 8.66114L6.38035 7.88285V9.08871L8.66709 6.84982L10.7778 8.91672L11.9112 7.80323L8.66752 4.62646L6.99625 6.26303L6.38035 5.65992L4.45055 7.54971C2.46505 9.49395 2.46505 12.6572 4.45055 14.6015C5.4433 15.5737 6.74705 16.0597 8.05124 16.0597C9.35495 16.0597 10.6595 15.5737 11.6522 14.6015C12.4873 13.7838 13.0089 12.7002 13.1202 11.5513C13.1503 11.2464 13.1511 10.9362 13.1227 10.6298L13.1219 10.6305Z"
								fill="#0000FE" />
						</g>
						<defs>
							<clipPath id="clip0_8329_9809">
								<rect width="16" height="19" fill="white" />
							</clipPath>
						</defs>
					</svg>
					<span class="text-sm font-medium text-base-100">GlacierDrop</span>
				</div>
			</div>
		</div>

		<!-- Progress Steps -->
		<div class="lg:w-[780px] mx-4 lg:mx-auto mb-24">
			<div class="flex items-center justify-center w-full font-medium">
				<div class="flex w-full justify-between h-[1px] border-b-[1px] border-[#343849]">
					<template v-for="(step, index) in steps" :key="step.id">
						<Step class="hidden lg:flex" :label="step.title" :stepIndex="index + 1" :currentStep="currentStep" />
						<Step class="lg:hidden" :label="`${index + 1}`" :stepIndex="index + 1" :currentStep="currentStep" />
					</template>
				</div>
			</div>
		</div>

		<!-- Main Content -->
		<div class="flex flex-col gap-5 mx-auto lg:flex-row lg:justify-center">
			<!-- Left Panel - Form -->
			<div class="space-y-3 w-full lg:max-w-[580px]">
				<!-- Verify Wallet Eligibility -->
				<div class="px-6 py-2 rounded-lg collapse bg-[#131627]">
					<input type="radio" class="absolute" :checked="openedVerifyWalletEligibility" />
					<div class="z-10 flex items-center justify-between gap-2 p-0 collapse-title"
						@click="toggleOpenedVerifyWalletEligibility">
						<h3 class="font-medium" :class="currentStep === 1 ? 'text-white' : 'text-gray-500'">
							Verify Wallet Eligibility
						</h3>
						<div class="flex items-center gap-2">
							<span v-if="isEligible" class="flex items-center gap-2 text-2sm text-success">
								<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
									<g clip-path="url(#clip0_8188_18670)">
										<path
											d="M7.99843 1.3335L8.0771 1.33816L8.11643 1.3435L8.1571 1.35216L8.2311 1.37483C8.28221 1.39367 8.3308 1.41875 8.37577 1.4495L8.4451 1.50416L8.6151 1.6495C9.95998 2.76654 11.6597 3.36636 13.4078 3.34083L13.6358 3.33416C13.7848 3.32736 13.9319 3.37077 14.0533 3.45743C14.1748 3.54409 14.2637 3.669 14.3058 3.81216C14.6334 4.92655 14.7336 6.09542 14.6006 7.24931C14.4675 8.4032 14.1038 9.51857 13.5312 10.5291C12.9586 11.5397 12.1886 12.4249 11.2671 13.132C10.3457 13.8391 9.29143 14.3538 8.1671 14.6455C8.05736 14.674 7.94217 14.674 7.83243 14.6455C6.70805 14.3539 5.65375 13.8393 4.73219 13.1322C3.81063 12.4251 3.04062 11.5399 2.46792 10.5293C1.89521 9.51876 1.5315 8.40336 1.3984 7.24943C1.26529 6.0955 1.36551 4.92659 1.6931 3.81216C1.73517 3.669 1.82406 3.54409 1.94553 3.45743C2.067 3.37077 2.21404 3.32736 2.3631 3.33416C4.18808 3.41759 5.97848 2.81682 7.38377 1.6495L7.5591 1.4995L7.6231 1.4495C7.66807 1.41875 7.71666 1.39367 7.76777 1.37483L7.84243 1.35216C7.86856 1.34585 7.89506 1.34118 7.92177 1.33816L7.99843 1.3335ZM10.4718 6.1955C10.4099 6.13351 10.3363 6.08434 10.2554 6.05079C10.1745 6.01724 10.0877 5.99997 10.0001 5.99997C9.91249 5.99997 9.82574 6.01724 9.74481 6.05079C9.66388 6.08434 9.59035 6.13351 9.52843 6.1955L7.3331 8.39016L6.4711 7.52883L6.40843 7.4735C6.27444 7.36989 6.10603 7.32117 5.93742 7.33723C5.7688 7.35329 5.61262 7.43293 5.5006 7.55998C5.38858 7.68702 5.32911 7.85194 5.33428 8.02124C5.33945 8.19054 5.40887 8.35152 5.52843 8.4715L6.86177 9.80483L6.92443 9.86016C7.0527 9.95966 7.21287 10.0089 7.37489 9.99875C7.53691 9.98857 7.68964 9.91961 7.80443 9.80483L10.4711 7.13816L10.5264 7.0755C10.6259 6.94723 10.6752 6.78706 10.665 6.62504C10.6548 6.46302 10.5859 6.31029 10.4711 6.1955H10.4718Z"
											fill="#50F187" />
									</g>
									<defs>
										<clipPath id="clip0_8188_18670">
											<rect width="16" height="16" fill="white" />
										</clipPath>
									</defs>
								</svg>
								<span>Eligible</span>
							</span>
							<inline-svg :src="chevronDownIcon" :style="{
								transform: openedVerifyWalletEligibility ? 'rotate(180deg)' : 'rotate(0deg)',
								transition: 'transform 0.3s ease',
							}" />
						</div>
					</div>
					<div class="z-10 p-0 collapse-content">
						<p class="mb-4 text-sm text-gray-400">
							Verify if your wallet qualifies for NIGHT token redemption based on the snapshot.
						</p>

						<div class="space-y-4">
							<div>
								<label class="block mb-2 text-sm text-gray-400">Connected Wallet Address</label>
								<input type="text" class="w-full px-4 py-3 text-sm bg-transparent border rounded-lg" :class="{
									'border-error': errorCheckingEligibility,
									'border-textbox-border': !errorCheckingEligibility
								}" :value="accountAddress" disabled />
								<span v-if="errorCheckingEligibility" class="mt-1 text-xs text-error">{{
									errorCheckingEligibility
								}} <span
										v-if="errorCheckingEligibility === 'Address provided is not eligible for an allocation of Night.'"><a
											target="_blank" class="text-primary"
											href="https://www.midnight.gd/faq?question=what-are-the-eligibility-criteria-for-the-glacier-drop-phase">Check
											eligibility criteria
										</a></span></span>
							</div>
							<div v-if="errorCheckingEligibility && errorCheckingEligibility !== 'Already Claimed'"
								class="mt-6 p-4 bg-[#2B2630] rounded-lg">
								<div class="flex items-center gap-3">
									<div class="w-5 h-5">
										<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
											<g clip-path="url(#clip0_8188_20074)">
												<path
													d="M10.6683 1.80417L15.3142 3.795C15.7142 3.96667 16.0333 4.28584 16.2058 4.68667L18.1958 9.33167C18.3792 9.75834 18.3792 10.2417 18.1958 10.6683L16.205 15.3142C16.0333 15.7142 15.7142 16.0333 15.3133 16.2058L10.6683 18.1958C10.2417 18.3792 9.75834 18.3792 9.33167 18.1958L4.68584 16.205C4.2851 16.0334 3.9658 15.7141 3.79417 15.3133L1.80417 10.6683C1.71366 10.4571 1.66699 10.2298 1.66699 10C1.66699 9.77024 1.71366 9.54286 1.80417 9.33167L3.795 4.68584C3.96667 4.28584 4.28584 3.96667 4.68667 3.79417L9.33167 1.80417C9.54286 1.71366 9.77024 1.66699 10 1.66699C10.2298 1.66699 10.4571 1.71366 10.6683 1.80417Z"
													stroke="#FFB784" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
												<path d="M10 6.6665V9.99984" stroke="#FFB784" stroke-width="1.5" stroke-linecap="round"
													stroke-linejoin="round" />
												<path d="M10 13.3335H10.01" stroke="#FFB784" stroke-width="1.5" stroke-linecap="round"
													stroke-linejoin="round" />
											</g>
											<defs>
												<clipPath id="clip0_8188_20074">
													<rect width="20" height="20" fill="white" />
												</clipPath>
											</defs>
										</svg>
									</div>
									<div class="text-[#FFB784] text-xs font-normal">
										<span class="font-extrabold">Not eligible on first check.</span>{{ " " }}
										This can happen due to network delays or snapshot synchronization. Please try checking again -
										eligibility data is still being updated
									</div>
								</div>
							</div>
							<button v-if="isLoggedIn" class="w-full h-10 btn btn-primary btn-sm" @click="checkEligibility">
								<span v-if="isChecking" class="loading loading-spinner loading-md">Checking...</span>
								<span v-else>Check Eligibility</span>
							</button>
							<button v-else class="w-full h-10 btn btn-primary btn-sm" @click="showConnectModal">
								Connect
							</button>
							<div v-if="errorCheckingEligibility" class="text-xs text-center text-white/50">Attempts: {{
								checkingEligibilityAttempts }} • Eligibility
								data
								updates frequently
							</div>
						</div>
					</div>
				</div>

				<!-- Enter Destination Address -->
				<div class="px-6 py-2 rounded-lg collapse bg-[#131627]">
					<input type="radio" class="absolute" :checked="openedEnterDestinationAddress" />
					<div class="z-10 flex items-center justify-between gap-2 p-0 collapse-title"
						@click="toggleOpenedEnterDestinationAddress">
						<h3 class="font-medium" :class="currentStep === 2 ? 'text-white' : 'text-gray-500'">
							Enter Destination Address
						</h3>
						<div class="flex items-center gap-2">
							<div v-if="isVerifiedDestinationAddress" class="flex items-center gap-2 text-2sm">{{
								`${destinationAddress?.slice(0, 5)}...${destinationAddress?.slice(-5)}` }}</div>
							<inline-svg :src="chevronDownIcon" :style="{
								transform: openedEnterDestinationAddress ? 'rotate(180deg)' : 'rotate(0deg)',
								transition: 'transform 0.3s ease',
							}" />
						</div>
					</div>
					<div class="z-10 p-0 collapse-content">
						<p class="mb-4 text-sm text-gray-400">
							Provide a fresh Cardano address where your NIGHT tokens will be sent.
						</p>
						<div class="grid w-full grid-cols-2 gap-3 text-2sm" :class="{ 'mb-8': hasCardanoAddress !== null }">
							<div class="flex items-center gap-3 p-4 rounded-lg bg-modal"
								:class="{ 'bg-primary/10 border border-primary': hasCardanoAddress === true }"
								@click="hasCardanoAddress = true">
								<inline-svg :src="squareRoundedCheck1Icon" />
								Yes, I have one
							</div>
							<div class="flex items-center gap-3 p-4 rounded-lg bg-modal"
								:class="{ 'bg-primary/10 border border-primary': hasCardanoAddress === false }"
								@click="hasCardanoAddress = false">
								<inline-svg :src="squareRoundedX1Icon" />No, I need to create
							</div>
						</div>

						<div v-if="hasCardanoAddress === false" class="mb-4">
							<!-- CTRL Wallet Creation Section -->
							<div class="p-6 rounded-lg border border-[#2a2d3e]">
								<!-- Header -->
								<div class="mb-4">
									<h3 class="mb-2 text-lg font-medium text-white">Create a new Cardano wallet with CTRL:</h3>
									<p class="text-sm text-white/50">
										CTRL is a secure, user-friendly Cardano wallet that's perfect for receiving your NIGHT tokens.
									</p>
								</div>

								<!-- Hero Image/Banner -->
								<img :src="midnightBanner" alt="CTRL Logo" class="mb-4" />

								<!-- Why CTRL Wallet Section -->
								<div class="mb-6">
									<h4 class="mb-4 text-base font-medium text-white">Why CTRL Wallet?</h4>
									<div class="space-y-2">
										<div class="flex items-center gap-3">
											<div class="flex items-center justify-center flex-shrink-0 w-5 h-5 rounded-full">
												<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
													<g clip-path="url(#clip0_8188_20052)">
														<path d="M2.5 6L5 8.5L10 3.5" stroke="#50F187" stroke-width="1.2" stroke-linecap="round"
															stroke-linejoin="round" />
													</g>
													<defs>
														<clipPath id="clip0_8188_20052">
															<rect width="12" height="12" fill="white" />
														</clipPath>
													</defs>
												</svg>
											</div>
											<span class="text-sm text-white/50">Secure and non-custodial</span>
										</div>
										<div class="flex items-center gap-3">
											<div class="flex items-center justify-center flex-shrink-0 w-5 h-5 rounded-full">
												<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
													<g clip-path="url(#clip0_8188_20052)">
														<path d="M2.5 6L5 8.5L10 3.5" stroke="#50F187" stroke-width="1.2" stroke-linecap="round"
															stroke-linejoin="round" />
													</g>
													<defs>
														<clipPath id="clip0_8188_20052">
															<rect width="12" height="12" fill="white" />
														</clipPath>
													</defs>
												</svg>
											</div>
											<span class="text-sm text-white/50">Easy to use interface</span>
										</div>
										<div class="flex items-center gap-3">
											<div class="flex items-center justify-center flex-shrink-0 w-5 h-5 rounded-full">
												<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
													<g clip-path="url(#clip0_8188_20052)">
														<path d="M2.5 6L5 8.5L10 3.5" stroke="#50F187" stroke-width="1.2" stroke-linecap="round"
															stroke-linejoin="round" />
													</g>
													<defs>
														<clipPath id="clip0_8188_20052">
															<rect width="12" height="12" fill="white" />
														</clipPath>
													</defs>
												</svg>
											</div>
											<span class="text-sm text-white/50">Full Cardano ecosystem support</span>
										</div>
										<div class="flex items-center gap-3">
											<div class="flex items-center justify-center flex-shrink-0 w-5 h-5 rounded-full">
												<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
													<g clip-path="url(#clip0_8188_20052)">
														<path d="M2.5 6L5 8.5L10 3.5" stroke="#50F187" stroke-width="1.2" stroke-linecap="round"
															stroke-linejoin="round" />
													</g>
													<defs>
														<clipPath id="clip0_8188_20052">
															<rect width="12" height="12" fill="white" />
														</clipPath>
													</defs>
												</svg>
											</div>
											<span class="text-sm text-white/50">Perfect for NIGHT token storage</span>
										</div>
									</div>
								</div>

								<!-- Action Buttons -->
								<div class="space-y-3">
									<a href="https://link.ctrl.xyz/?campaign=midnightclaim&source=secondswap" target="_blank">
										<button class="w-full h-10 font-medium text-black transition-colors rounded-lg btn-sm bg-primary">
											Create CTRL Wallet
										</button>
									</a>
									<button class="w-full text-sm font-medium transition-colors bg-transparent rounded-lg text-primary">
										View Setup Guide
									</button>
								</div>

								<!-- Important Notice -->
								<div class="mt-6 p-4 bg-[#2B2630] rounded-lg">
									<div class="flex items-center gap-3">
										<div class="w-5 h-5">
											<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
												<g clip-path="url(#clip0_8188_20074)">
													<path
														d="M10.6683 1.80417L15.3142 3.795C15.7142 3.96667 16.0333 4.28584 16.2058 4.68667L18.1958 9.33167C18.3792 9.75834 18.3792 10.2417 18.1958 10.6683L16.205 15.3142C16.0333 15.7142 15.7142 16.0333 15.3133 16.2058L10.6683 18.1958C10.2417 18.3792 9.75834 18.3792 9.33167 18.1958L4.68584 16.205C4.2851 16.0334 3.9658 15.7141 3.79417 15.3133L1.80417 10.6683C1.71366 10.4571 1.66699 10.2298 1.66699 10C1.66699 9.77024 1.71366 9.54286 1.80417 9.33167L3.795 4.68584C3.96667 4.28584 4.28584 3.96667 4.68667 3.79417L9.33167 1.80417C9.54286 1.71366 9.77024 1.66699 10 1.66699C10.2298 1.66699 10.4571 1.71366 10.6683 1.80417Z"
														stroke="#FFB784" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
													<path d="M10 6.6665V9.99984" stroke="#FFB784" stroke-width="1.5" stroke-linecap="round"
														stroke-linejoin="round" />
													<path d="M10 13.3335H10.01" stroke="#FFB784" stroke-width="1.5" stroke-linecap="round"
														stroke-linejoin="round" />
												</g>
												<defs>
													<clipPath id="clip0_8188_20074">
														<rect width="20" height="20" fill="white" />
													</clipPath>
												</defs>
											</svg>
										</div>
										<div class="text-[#FFB784] text-xs">
											<span class="font-medium">Important:</span>{{ " " }}
											After creating your CTRL wallet, copy your receiving address and paste it below. Make sure it's
											a
											fresh address with no transaction history.
										</div>
									</div>
								</div>
							</div>
						</div>

						<div v-if="hasCardanoAddress !== null">
							<label class="text-sm">Cardano Destination Address</label>
							<input type="text" v-model="destinationAddress"
								class="w-full px-4 py-3 mt-3 text-sm bg-transparent border rounded-lg" :class="{
									'border-error': errorVerifyingDestinationAddress,
									'border-textbox-border': !errorVerifyingDestinationAddress
								}" @input="errorVerifyingDestinationAddress = null" />
							<span v-if="errorVerifyingDestinationAddress" class="mt-1 text-xs text-error">{{
								errorVerifyingDestinationAddress
							}}</span>
							<p class="text-xs text-[#84858E] mt-3">
								Must start with "addr1" and be at least 100 characters long
							</p>
							<button class="w-full h-10 mt-6 btn btn-primary btn-sm" :class="{ 'opacity-50': !destinationAddress }"
								@click="verifyDestinationAddress">
								<span v-if="isChecking" class="loading loading-spinner loading-md">Checking...</span>
								<span v-else>Continue</span>
							</button>
						</div>
					</div>
				</div>

				<!-- Accept Terms & Conditions -->
				<div class="px-6 py-2 rounded-lg collapse bg-[#131627]">
					<input type="radio" class="absolute" :checked="openedAcceptTermsConditions" />
					<div class="z-10 flex items-center justify-between gap-2 p-0 collapse-title"
						@click="toggleOpenedAcceptTermsConditions">
						<h3 class="font-medium" :class="currentStep === 3 ? 'text-white' : 'text-gray-500'">
							Accept Terms & Conditions
						</h3>
						<div class="flex items-center gap-2">
							<span v-if="termsAccepted1 && termsAccepted2" class="flex items-center gap-2 text-2sm">Accepted</span>
							<inline-svg :src="chevronDownIcon" :style="{
								transform: openedAcceptTermsConditions ? 'rotate(180deg)' : 'rotate(0deg)',
								transition: 'transform 0.3s ease',
							}" />
						</div>
					</div>
					<div class="z-10 p-0 collapse-content">
						<p class="mb-8 text-sm text-white/50">
							By proceeding with this claim, you agree to the <a class="text-primary"
								href="https://claim.midnight.gd/static/terms-conditions-gd.pdf" target="_blank">terms and
								conditions.</a>
						</p>

						<!-- Important Notice -->
						<div class="mt-5 p-4 bg-[#2B2630] rounded-lg">
							<div class="flex items-center gap-3">
								<div class="w-5 h-5">
									<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
										<g clip-path="url(#clip0_8188_20414)">
											<path
												d="M10.6683 1.80417L15.3142 3.795C15.7142 3.96667 16.0333 4.28584 16.2058 4.68667L18.1958 9.33167C18.3792 9.75834 18.3792 10.2417 18.1958 10.6683L16.205 15.3142C16.0333 15.7142 15.7142 16.0333 15.3133 16.2058L10.6683 18.1958C10.2417 18.3792 9.75834 18.3792 9.33167 18.1958L4.68584 16.205C4.2851 16.0334 3.9658 15.7141 3.79417 15.3133L1.80417 10.6683C1.71366 10.4571 1.66699 10.2298 1.66699 10C1.66699 9.77024 1.71366 9.54286 1.80417 9.33167L3.795 4.68584C3.96667 4.28584 4.28584 3.96667 4.68667 3.79417L9.33167 1.80417C9.54286 1.71366 9.77024 1.66699 10 1.66699C10.2298 1.66699 10.4571 1.71366 10.6683 1.80417Z"
												stroke="#FFB784" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
											<path d="M10 6.6665V9.99984" stroke="#FFB784" stroke-width="1.5" stroke-linecap="round"
												stroke-linejoin="round" />
											<path d="M10 13.3335H10.01" stroke="#FFB784" stroke-width="1.5" stroke-linecap="round"
												stroke-linejoin="round" />
										</g>
										<defs>
											<clipPath id="clip0_8188_20414">
												<rect width="20" height="20" fill="white" />
											</clipPath>
										</defs>
									</svg>
								</div>
								<div class="text-[#FFB784] text-xs">
									<span class="font-medium">Important:</span>{{ " " }}
									Cryptocurrency investments carry significant risk. NIGHT tokens may lose value, and trading while
									locked
									is not guaranteed. Only claim tokens if you understand and accept these risks. Never invest more than
									you
									can afford to lose.
								</div>
							</div>
						</div>

						<div class="flex items-center gap-2 mt-6">
							<input id="termsAccepted1" type="checkbox" v-model="termsAccepted1"
								class="checkbox checkbox-primary h-4 w-4 !rounded-[2px]" :true-value="true" :false-value="false" />
							<label for="termsAccepted1" class="text-sm text-white/50">I have read, understood, and agree to the Terms
								&
								Conditions for
								claiming
								NIGHT
								tokens through
								SecondSwap.</label>
						</div>

						<div class="flex items-center gap-2 mt-6">
							<input id="termsAccepted2" type="checkbox" v-model="termsAccepted2"
								class="checkbox checkbox-primary h-4 w-4 !rounded-[2px]" :true-value="true" :false-value="false" />
							<label for="termsAccepted2" class="text-sm text-white/50">I acknowledge the risks associated with
								cryptocurrency investments and
								understand
								that trading while
								locked is not guaranteed.</label>
						</div>

						<button class="w-full h-10 mt-6 btn btn-primary btn-sm"
							:class="{ 'opacity-50': !termsAccepted1 || !termsAccepted2 }" @click="acceptTermsAndConditions">
							<span v-if="isChecking" class="loading loading-spinner loading-md">Checking...</span>
							<span v-else>Continue</span>
						</button>
					</div>
				</div>

				<!-- Link Telegram -->
				<div class="px-6 py-2 rounded-lg collapse bg-[#131627]">
					<input type="radio" class="absolute" :checked="openedLinkTelegram" />
					<div class="z-10 flex items-center justify-between gap-2 p-0 collapse-title"
						@click="toggleOpenedLinkTelegram">
						<h3 class="font-medium" :class="currentStep === 4 ? 'text-white' : 'text-gray-500'">
							Link Telegram (Optional)
						</h3>
						<div class="flex items-center gap-2">
							<span v-if="telegramLinked" class="flex items-center gap-2 text-2sm">Linked</span>
							<inline-svg :src="chevronDownIcon" :style="{
								transform: openedAcceptTermsConditions ? 'rotate(180deg)' : 'rotate(0deg)',
								transition: 'transform 0.3s ease',
							}" />
						</div>
					</div>
					<div class="z-10 p-0 collapse-content">
						<p class="mb-4 text-sm text-gray-400">
							You must link telegram account to be eligible for the bonus 2swap tokens
						</p>
						<div class="flex justify-center">
							<img v-if="!telegramLinked" :src="linkTelegramImage" class="w-[100px] h-[100px]" />
							<img v-else :src="linkTelegramSuccessImage" class="w-[100px] h-[100px]" />
						</div>
						<LinkTelegramButton v-if="!telegramLinked" @on-success="handleTelegramLinkSuccess">
							<div class="w-full h-10 mt-6 btn btn-primary btn-sm">
								<img :src="telegramIcon" alt="telegram icon"></img>
								<span>Link Now</span>
							</div>
						</LinkTelegramButton>
						<div v-else class="text-center text-success text-2s">You’ve already linked to telegram account</div>
						<div class="flex justify-center mt-2 text-2sm text-primary">
							<button @click="skipLinkTelegram">Skip this step</button>
						</div>
					</div>
				</div>

				<!-- Sign Claim Message -->
				<div class="px-6 py-2 rounded-lg collapse bg-[#131627]">
					<input type="radio" class="absolute" :checked="openedSignClaimMessage" />
					<div class="z-10 flex items-center justify-between gap-2 p-0 collapse-title"
						@click="toggleOpenedSignClaimMessage">
						<h3 class="font-medium" :class="currentStep === 5 ? 'text-white' : 'text-gray-500'">
							Sign Claim Message
						</h3>
						<inline-svg :src="chevronDownIcon" :style="{
							transform: openedSignClaimMessage ? 'rotate(180deg)' : 'rotate(0deg)',
							transition: 'transform 0.3s ease',
						}" />
					</div>
					<div class="z-10 p-0 collapse-content">
						<button class="w-full h-10 mt-6 btn btn-primary btn-sm" @click="signToConfirmClaim"
							:class="{ 'opacity-50': !accountAddress || !destinationAddress || !termsAccepted1 || !termsAccepted2 }">
							<span v-if="isChecking" class="loading loading-spinner loading-md">Checking...</span>
							<span v-else>Sign to Confirm Claim</span>
						</button>
					</div>
				</div>
			</div>

			<!-- Right Panel - Preview -->
			<div class="p-6 rounded-lg w-full lg:max-w-[460px]" style="background-color: #131627">
				<h3 class="mb-6 font-medium text-white">Preview Claim</h3>

				<!-- Token Info -->
				<div class="mb-6 text-center">
					<div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gray-700 rounded-full">
						<img :src="nightToken" />
					</div>
					<h4 class="mb-2 text-lg font-medium text-white">NIGHT</h4>
				</div>

				<!-- Details -->
				<div class="space-y-4">
					<div class="text-xs font-medium tracking-widest">ELIGIBILITY CHECK</div>
					<div class="flex justify-between text-sm">
						<span class="text-white/50">Wallet Address</span>
						<span class="text-sm text-white">{{ accountAddress?.slice(0, 4) }}...{{ accountAddress?.slice(-4) }}</span>
					</div>
					<div class="flex justify-between text-sm">
						<span class="text-white/50">Network</span>
						<span class="text-white">{{ networkName }}</span>
					</div>
					<div class="px-5 py-4 pt-4 rounded-lg" :class="{
						'bg-modal': !isEligible,
						'bg-success bg-opacity-[8%]': isEligible
					}">
						<div class="mb-2 text-sm text-white/50">Allocation Size</div>
						<div class="text-xl font-bold" :class="{ 'text-success': isEligible, 'text-white': !isEligible }">{{
							toBigNumber(allocationAmount).div(toBigNumber(1e6)).toFixed(6) }} NIGHT
						</div>
						<div class="flex items-center gap-6 mt-1 text-xs text-white/50">
							<span>Address: {{ isEligible || errorCheckingEligibility === "Already Claimed" ?
								`${accountAddress?.slice(0,
									4)}...${accountAddress?.slice(-4)}` :
								'----------'
							}}</span>
							<span>Network: <span class="capitalize">{{ isEligible || errorCheckingEligibility === "Already Claimed" ?
								networkName : '----------' }}</span></span>
						</div>
					</div>
					<div class="pt-4 border-t border-textbox-border">
						<div class="mb-2 text-sm text-white/50">DESTINATION ADDRESS</div>
						<div class="flex items-start justify-between gap-4">
							<div class="text-sm text-white/50 whitespace-nowrap">Cardano Address</div>
							<div class="text-sm text-white break-all">
								{{ destinationAddress || '----------' }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Bottom Banner -->
		<div class="max-w-[1060px] mx-auto my-5 lg:mt-10">
			<img :src="airdropBanner" />
		</div>
		<ModalMidnightSummary :amount="allocationAmount" :walletEligibility="accountAddress" :network="networkName"
			:destinationAddress="destinationAddress" :submissionTime="new Date().toLocaleString()" />
	</div>
</template>

<script setup>
import { ref, computed } from 'vue'
import InlineSvg from 'vue-inline-svg'


// Import the chevron down icon
import chevronDownIcon from '~/assets/images_new/icons/chevron-down.svg'
import squareRoundedCheck1Icon from '~/assets/images_new/icons/square-rounded-check-1.svg'
import squareRoundedX1Icon from '~/assets/images_new/icons/square-rounded-x-1.svg'
import midnightBanner from '~/assets/images_new/common/midnight-banner.png'
import airdropBanner from '~/assets/images_new/common/airdrop-banner.png'
import nightToken from '~/assets/images_new/common/night-token.png'
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/vue'
import telegramIcon from "~/assets/images_new/icons/social_media/telegram2.svg";
import linkTelegramImage from '~/assets/images_new/common/link-telegram.png'
import linkTelegramSuccessImage from '~/assets/images_new/common/link-telegram-success.png'

import Step from '../Step.vue'
import { useMutation } from '@tanstack/vue-query'
import bs58 from 'bs58';

const API_URL = useRuntimeConfig().public.apiUrl

const midnightCheckEligibilityMutation = useMutation({
	mutationFn: async (props) => {
		const { address, network } = props
		const res = await fetch(`${API_URL}/midnight/eligibility/${address}?network=${network.toLowerCase()}`)
		return await res.json()
	}
})

const midnightVerifyDestinationAddressMutation = useMutation({
	mutationFn: async (props) => {
		const { address } = props
		const res = await fetch(`${API_URL}/midnight/claims/${address}/is-fresh`)
		return await res.json()
	}
})

const midnightSubmitClaimsMutation = useMutation({
	mutationFn: async (props) => {
		const { address, amount, destAddress, signature, network } = props
		const res = await fetch(`${API_URL}/midnight/claims`, {
			method: "POST",
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				claims: [
					{ address, amount, destAddress, signature }
				],
				network
			})
		})
		return await res.json()
	}
})

const newWeb3Store = useNewWeb3Store();
const { isLoggedIn, web3Kit } = storeToRefs(newWeb3Store);
const { networksByChainId } = newWeb3Store;

const openModal = inject("openModal")
const closeModal = inject("closeModal")

watch(isLoggedIn, async () => {
	currentStep.value = 1
	isChecking.value = false
	isEligible.value = false
	errorCheckingEligibility.value = null
	checkingEligibilityAttempts.value = 0
	destinationAddress.value = ''
	isVerifiedDestinationAddress.value = false
	errorVerifyingDestinationAddress.value = null
	termsAccepted1.value = false
	termsAccepted2.value = false
	telegramLinked.value = false
	openedVerifyWalletEligibility.value = true
	openedEnterDestinationAddress.value = false
	openedAcceptTermsConditions.value = false
	openedLinkTelegram.value = false
	openedSignClaimMessage.value = false
	hasCardanoAddress.value = null

	if (isLoggedIn.value) {
		const userApiResponse = await api.apiCall("GET", "/user");
		const telegramHandle = userApiResponse.data.message.telegram
		if (telegramHandle) {
			telegramLinked.value = true
		}
	}
})

// State management
const account = useAppKitAccount()
const accountAddress = computed(() => account.value.address)
const networkData = useAppKitNetwork()
const networkSymbol = computed(() => networksByChainId[networkData.value.chainId]?.nativeCurrency.symbol)
const networkName = computed(() => networksByChainId[networkData.value.chainId]?.name)
const currentStep = ref(1)
const isChecking = ref(false)
const isEligible = ref(false)
const errorCheckingEligibility = ref(null)
const checkingEligibilityAttempts = ref(0)
const destinationAddress = ref('')
const isVerifiedDestinationAddress = ref(false)
const errorVerifyingDestinationAddress = ref(null)
const termsAccepted1 = ref(false)
const termsAccepted2 = ref(false)
const telegramLinked = ref(false)
const openedVerifyWalletEligibility = ref(true)
const openedEnterDestinationAddress = ref(false)
const openedAcceptTermsConditions = ref(false)
const openedLinkTelegram = ref(false)
const openedSignClaimMessage = ref(false)
const hasCardanoAddress = ref(null) // true, false, null

const toggleOpenedVerifyWalletEligibility = () => {
	openedVerifyWalletEligibility.value = !openedVerifyWalletEligibility.value
}

const toggleOpenedEnterDestinationAddress = () => {
	openedEnterDestinationAddress.value = !openedEnterDestinationAddress.value
}

const toggleOpenedAcceptTermsConditions = () => {
	openedAcceptTermsConditions.value = !openedAcceptTermsConditions.value
}

const toggleOpenedLinkTelegram = () => {
	openedLinkTelegram.value = !openedLinkTelegram.value
}

const toggleOpenedSignClaimMessage = () => {
	openedSignClaimMessage.value = !openedSignClaimMessage.value
}

// Mock data
const allocationAmount = ref(0)
const allocationNetwork = ref('')

function showConnectModal() {
	document.getElementById("connectModal").checked = true;
}

// Computed properties
const steps = computed(() => [
	{
		id: 1,
		title: 'Verify Wallet Eligibility',
		completed: currentStep.value > 1,
		active: currentStep.value === 1
	},
	{
		id: 2,
		title: 'Enter Destination Address',
		completed: currentStep.value > 2,
		active: currentStep.value === 2
	},
	{
		id: 3,
		title: 'Accept Terms & Conditions',
		completed: currentStep.value > 3,
		active: currentStep.value === 3
	},
	{
		id: 4,
		title: 'Link Telegram (Optional)',
		completed: currentStep.value > 4,
		active: currentStep.value === 4
	},
	{
		id: 5,
		title: 'Sign Claim Message',
		completed: currentStep.value > 5,
		active: currentStep.value === 5
	}
])

// Methods
const checkEligibility = async () => {
	isChecking.value = true
	isEligible.value = false
	errorCheckingEligibility.value = null
	checkingEligibilityAttempts.value++
	// Simulate API call
	try {
		const response = await midnightCheckEligibilityMutation.mutateAsync({
			address: accountAddress.value,
			network: networkSymbol.value
		})
		if (response.status === "success") {
			if (!response.message.amount) {
				throw new Error("Address provided is not eligible for an allocation of Night.")
			} else {
				const checkAlreadyClaimed = await fetch(`${API_URL}/midnight/claims?network=${networkSymbol.value}&originalAddresses=${accountAddress.value}`)
				const checkAlreadyClaimedResponse = await checkAlreadyClaimed.json()
				allocationAmount.value = response.message.amount
				if (checkAlreadyClaimedResponse.message.claims.length > 0) {
					throw new Error("Already Claimed")
				}
				openedVerifyWalletEligibility.value = false
				openedEnterDestinationAddress.value = true
				isEligible.value = true
				currentStep.value = 2
			}
		}
	} catch (error) {
		errorCheckingEligibility.value = error.message || "Something went wrong."
	}
	finally {
		isChecking.value = false
	}
}

const verifyDestinationAddress = async () => {
	isChecking.value = true
	isVerifiedDestinationAddress.value = false
	errorVerifyingDestinationAddress.value = null
	try {
		const response = await midnightVerifyDestinationAddressMutation.mutateAsync({
			address: destinationAddress.value
		})
		if (response.status === "success") {
			if (response.message.isFresh) {
				currentStep.value = 3
				isVerifiedDestinationAddress.value = true
				openedEnterDestinationAddress.value = false
				openedAcceptTermsConditions.value = true
			} else {
				throw new Error("Invalid address")
			}
		} else {
			throw new Error("Something went wrong")
		}
	} catch (error) {
		errorVerifyingDestinationAddress.value = error.message || "Something went wrong."
	}
	finally {
		isChecking.value = false
	}
}

const acceptTermsAndConditions = async () => {
	if (!termsAccepted1.value || !termsAccepted2.value) return;
	isChecking.value = true
	await new Promise(resolve => setTimeout(() => {
		resolve()
		currentStep.value = 4
		openedAcceptTermsConditions.value = false
		openedLinkTelegram.value = true
	}, 1000))
	isChecking.value = false
}

const skipLinkTelegram = () => {
	currentStep.value = 5
	openedLinkTelegram.value = false
	openedSignClaimMessage.value = true
}

const handleTelegramLinkSuccess = async () => {
	telegramLinked.value = true
	skipLinkTelegram()
}

function createMessage(shade, destAddress) {
	const termsAndConditionsHash =
		"31a6bab50a84b8439adcfb786bb2020f6807e6e8fda629b424110fc7bb1c6b8b";
	return `STAR ${shade} to ${destAddress} ${termsAndConditionsHash}`;
}

const transformSignature = (signature, network) => {
	switch (network) {
		case "ETH": {
			return signature.slice(2);
		}
		case "SOL": {
			return Buffer.from(bs58.decode(signature)).toString("hex");
		}
	}
	return signature;
}

const signToConfirmClaim = async () => {
	if (!accountAddress.value
		|| !isEligible.value
		|| !destinationAddress.value
		|| !isVerifiedDestinationAddress.value
		|| !termsAccepted1.value
		|| !termsAccepted2.value) return

	isChecking.value = true

	console.log("Submit data", {
		address: accountAddress.value,
		amount: allocationAmount.value,
		destAddress: destinationAddress.value,
		network: networkSymbol.value,
	})

	try {
		const shade = allocationAmount.value
		const destAddress = destinationAddress.value
		const message = createMessage(shade, destAddress)
		console.log("Sign message with: ", { shade, destAddress })
		const signResult = await web3Kit.value.signMessage(accountAddress, message)
		console.log("Sign result", signResult)

		const response = await midnightSubmitClaimsMutation.mutateAsync({
			address: accountAddress.value,
			amount: shade,
			destAddress: destAddress,
			signature: transformSignature(signResult.signature, networkSymbol.value),
			network: networkSymbol.value,
		})
		if (response.status === "success") {
			document.getElementById("midnightSummaryModal").checked = true
		} else {
			throw new Error("Something went wrong")
		}
	} catch (error) {
		console.error("error while claiming:", error)
		openModal({
			type: "error",
			title: "Transaction Declined",
			message: "Something went wrong. Please try again",
			action: {
				name: "Try Again",
				onClick: () => {
					closeModal()
					signToConfirmClaim()
				}
			}
		})
	} finally {
		isChecking.value = false
	}
}

</script>

<style scoped>
/* Custom styles if needed */
</style>
