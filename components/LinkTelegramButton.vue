<template>
	<button class="w-full" :type="type" @click="linkToTelegram">
		<slot />
	</button>
	<ModalConnect ref="connectModal"></ModalConnect>
</template>

<script setup>
import { useAppKitAccount, useAppKitNetwork } from "@reown/appkit/vue";
import api from "../utils/api";
import dayjs from "dayjs";
import { getAddress } from "ethers";

const props = defineProps({
	type: String,
})

const web3Store = useWeb3Store();
const newWeb3Store = useNewWeb3Store();
const { isLoggedIn, web3Kit } = storeToRefs(newWeb3Store);
const { networksByChainId } = newWeb3Store;
const account = useAppKitAccount();
const networkData = useAppKitNetwork()

const emit = defineEmits(["onSuccess"])

const connectModal = ref(null);
const openModal = inject("openModal")

const showLoginModal = () => {
	connectModal.value.handleClick();
	document.getElementById("connectModal").checked = true;
}

const linkToTelegram = async () => {
	if (!isLoggedIn.value) {
		showLoginModal()
		return
	}

	openModal({
		type: "loading",
		title: "Linking your telegram account",
		message: "Proceed in telegram to complete linking step",
	})

	try {
		const timestamp = dayjs().valueOf()
		const networkSymbol = networksByChainId[networkData.value.chainId]?.nativeCurrency.symbol
		const address = networkSymbol === "ETH" ? getAddress(account.value.address) : account.value.address

		console.log("web3Kit.value", web3Kit.value)
		const signMessageResponse = await web3Kit.value?.signMessage(
			account.value.address,
			`Connect with SecondSwap Telegram bot\n` +
			`Address: ${address}\n` +
			`Nonce: ${timestamp}`
		)

		const linkTelegramResponse = await api.apiCall("POST", "/telegram/link", {
			network: networkSymbol,
			nonce: Number(timestamp),
			signature: signMessageResponse.signature,
		})

		window.open(linkTelegramResponse.data.message.url, "_blank")

		let timeoutID;
		let intervalID = setInterval(async () => {
			if (!timeoutID) {
				timeoutID = setTimeout(() => {
					clearInterval(intervalID);
					openModal({
						type: "error",
						title: "Linking Unsucessful",
						message: "Oops we missed your connection, please try again",
						action: {
							name: "Try Again",
							onClick: linkToTelegram,
						}
					})
				}, 15000)
			}
			const res = await api.apiCall("GET", "/telegram/link/status");
			const status = res.data.message.status
			if (status !== "pending") {
				clearInterval(intervalID);

				if (status === "linked") {
					const userApiResponse = await api.apiCall("GET", "/user");
					const telegramHandle = userApiResponse.data.message.telegram
					openModal({
						type: "success",
						title: "Telegram Linked",
						message: `Your telegram acount @${telegramHandle} was successfully linked`,
					})
					clearTimeout(timeoutID)
					emit("onSuccess")
				} else {
					openModal({
						type: "error",
						title: "Linking Unsucessful",
						message: "Oops we missed your connection, please try again",
						action: {
							name: "Try Again",
							onClick: linkToTelegram,
						}
					})
				}
			}
		}, 2000);
	} catch (error) {
		console.error(error)
		if (error.status === 400) {
			openModal({
				type: "error",
				title: "Linking Unsucessful",
				message: "Telegram account is already linked",
			})
		} else {
			openModal({
				type: "error",
				title: "Linking Unsucessful",
				message: "Oops we missed your connection, please try again",
				action: {
					name: "Try Again",
					onClick: linkToTelegram,
				}
			})
		}
	}
}


</script>

<style scoped></style>
