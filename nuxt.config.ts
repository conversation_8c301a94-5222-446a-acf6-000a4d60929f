// https://nuxt.com/docs/api/configuration/nuxt-config
import { resolve } from "path";
import { fileURLToPath, URL } from "node:url";

export default defineNuxtConfig({
  vite: {
    optimizeDeps: {
      esbuildOptions: {
        target: "esnext",
      },
    },
  },
  nitro: {
    esbuild: {
      options: {
        target: "esnext",
      },
    },
    compressPublicAssets: true,
    // routeRules: {
    //   // Apply compression to all routes
    //   '/**': {
    //     headers: {
    //       'Content-Encoding': 'gzip'
    //     }
    //   }
    // },
    prerender: {
      crawlLinks: false,
      routes: ["/"],
      ignore: ["/**/*"],
    },
    externals: {
      inline: ["@noble/hashes", "@noble/curves"],
    }
  },
  // routeRules: {
  //   '/': { ssr: true },
  //   '/**': { ssr: false }
  // },
  compatibilityDate: "2024-04-03",
  devtools: { enabled: true },
  runtimeConfig: {
    public: {
      // Get projectId from https://cloud.reown.com
      reownProjectId: process.env.NUXT_REOWN_PROJECT_ID,
      mode: process.env.NUXT_MODE,
      web3Mode: process.env.NUXT_WEB3_MODE,
      web3Network: process.env.NUXT_WEB3_NETWORK,
      bidOnly: process.env.NUXT_BID_ONLY || "true",
      baseUrl: process.env.NUXT_BASE_URL,
      apiUrl: process.env.NUXT_API_BASE_URL,

      marketplaceAddress: process.env.NUXT_MARKETPLACE_ADDR,
      marketplaceSettingAddress: process.env.NUXT_MARKETPLACE_SETTING_ADDR,
      usdtAddress: process.env.NUXT_USDT_ADDR,
      jsonRpcUrl: process.env.NUXT_JSON_RPC_URL,
      explorerUrl: process.env.NUXT_EXPLORER,

      avaxMarketplaceAddress: process.env.NUXT_AVAX_MARKETPLACE_ADDR,
      avaxMarketplaceSettingAddress: process.env.NUXT_AVAX_MARKETPLACE_SETTING_ADDR,
      avaxUsdtAddress: process.env.NUXT_AVAX_USDT_ADDR,
      avaxJsonRpcUrl: process.env.NUXT_AVAX_JSON_RPC_URL,
      avaxExplorerUrl: process.env.NUXT_AVAX_EXPLORER,
      // AVAX Network settings
      // avaxMarketplaceAddress: process.env.NUXT_AVAX_MARKETPLACE_ADDR,
      // avaxMarketplaceSettingAddress:
      //   process.env.NUXT_AVAX_MARKETPLACE_SETTING_ADDR,
      // avaxUsdtAddress: process.env.NUXT_AVAX_USDT_ADDR,
      // avaxExplorerUrl: process.env.NUXT_AVAX_EXPLORER,
      // avaxJsonRpcUrl: process.env.NUXT_AVAX_JSON_RPC_URL,

      gtm: {
        id: process.env.NUXT_GTM_ID!,
        queryParams: {
          // Add URL query string when loading gtm.js with GTM ID (required when using custom environments)
          gtm_auth: process.env.NUXT_GTM_AUTH!,
          gtm_preview: process.env.NUXT_GTM_PREVIEW!,
          gtm_cookies_win: process.env.NUXT_GTM_COOKIES_WIN!,
        },
      },
      feedbackFormUrl: process.env.NUXT_FEEDBACK_FORM_URL,
    },
  },

  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {},
    },
  },

  plugins: [
    { src: "~/plugins/dayjs.client.js", mode: "client" },
    { src: "~/plugins/chartjs.client.js", mode: "client" },
    // { src: "~/plugins/preline.client.ts", mode: "client" },
  ],

  css: ["~/assets/main.css"],
  modules: [
    "@pinia/nuxt",
    "@nuxtjs/tailwindcss",
    "@zadigetvoltaire/nuxt-gtm",
    "nuxt-security",
  ],
  security: {
    headers: {
      contentSecurityPolicy: {
        "base-uri": ["'self'"],
        "font-src": ["'self'", "https:", "data:", "https://client.crisp.chat"],
        "form-action": ["'self'"],
        "frame-ancestors": ["'self'"],
        "img-src": ["'self'", "data:", "https:", "blob:", "https://image.crisp.chat", "https://client.crisp.chat"],
        "object-src": ["'none'"],
        "script-src-attr": [],
        "style-src": ["'self'", "https:", "'unsafe-inline'", "https://client.crisp.chat"],
        "script-src": [
          "'self'",
          "https:",
          "'unsafe-inline'",
          "'strict-dynamic'",
          "'nonce-{{nonce}}'",
          "https://www.googletagmanager.com",
          "https://client.crisp.chat",
        ],
        "connect-src": [
          "'self'",
          "data:",
          "https://www.google-analytics.com",
          "https://stats.g.doubleclick.net",
          "https://client.crisp.chat",
          "https://storage.crisp.chat",
          "wss://client.relay.crisp.chat",
          "https://api.web3modal.org",
          "https://rpc.walletconnect.org",
          "https://pulse.walletconnect.org",
          "wss://relay.walletconnect.org",
          "https://verify.walletconnect.org",
          "https://cardano-preview.blockfrost.io/api/v0/epochs/latest/parameters",
          process.env.NUXT_API_BASE_URL,
          process.env.NUXT_JSON_RPC_URL,
          process.env.NUXT_AVAX_JSON_RPC_URL,
        ].filter(Boolean) as string[],
        "frame-src": ["'self'", "https://www.googletagmanager.com/", "https://game.crisp.chat", "https://verify.walletconnect.org"],
        "upgrade-insecure-requests": true,
      },
    },
    nonce: true,
  },
});
