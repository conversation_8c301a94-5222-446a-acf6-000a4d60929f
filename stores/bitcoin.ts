import { Buff<PERSON> } from "buffer";
globalThis.Buffer = globalThis.Buffer || Buffer;
import { useAppKitProvider } from "@reown/appkit/vue";
import type { Web3Kit } from "~/types/web3Kit";
import type { BitcoinConnector } from "@reown/appkit-adapter-bitcoin";

export class BitcoinKit implements Web3Kit {
  async signMessage(address: string, message: string) {
    const { walletProvider: proxiedWalletProvider } =
      useAppKitProvider<BitcoinConnector>("bip122");
    if (!proxiedWalletProvider) {
      throw new Error("Wallet provider not found");
    }
    if (!address) {
      throw new Error("missing address");
    }
    const walletProvider = toRaw(proxiedWalletProvider);
    try {
      const signature = await walletProvider.signMessage({
        address,
        message,
      });
      return {
        signature: signature,
      };
    } catch (error) {
      console.error(`error signing: ${error}`);
      throw error;
    }
  }
  claimToken: (tokenAddress: string) => Promise<boolean> = async () => {
    return false;
  };
  getNativeBalance: (address: string) => Promise<string> = async () => {
    return "0";
  };
}
