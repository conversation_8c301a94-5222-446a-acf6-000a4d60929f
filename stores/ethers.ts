import { <PERSON><PERSON><PERSON> } from "buffer";
globalThis.Buffer = globalThis.Buffer || Buffer
import { useAppKitAccount, useAppKitProvider, type UseAppKitAccountReturn } from "@reown/appkit/vue";
import { Contract } from "ethers";
import { BrowserProvider, formatEther } from "ethers";
import type { Eip1193Provider } from "ethers";
import type { Web3Kit } from "~/types/web3Kit";
import planAbi from '../abi/plan.json';
export class EthersKit implements Web3Kit {
	async signMessage(_address: string, message: string) {
		const { walletProvider: proxiedWalletProvider } = useAppKitProvider<Eip1193Provider>("eip155")
		if (!proxiedWalletProvider) {
			throw new Error("Wallet provider not found")
		}
		const walletProvider = toRaw(proxiedWalletProvider)
		const signer = await new BrowserProvider(walletProvider).getSigner();
		try {
				const signature = await signer.signMessage(message);
				return {
					signature: signature,
				};
		} catch (error) {
				console.error(`error signing: ${error}`);
				throw error;
		}
	}

	async getNativeBalance(address: string): Promise<string> {
		const { walletProvider: proxiedWalletProvider } = useAppKitProvider<Eip1193Provider>("eip155")
		if (!proxiedWalletProvider) {
			throw new Error("Ethers provider not found");
		}
		const walletProvider = toRaw(proxiedWalletProvider)
		const balance = await new BrowserProvider(walletProvider).getBalance(address);
		return formatEther(balance);
	}

	async claimToken(tokenAddress: string) {
		const { walletProvider: proxiedWalletProvider } = useAppKitProvider<Eip1193Provider>("eip155")
		if (!proxiedWalletProvider) {
			throw new Error("Ethers provider not found");
		}
		const walletProvider = toRaw(proxiedWalletProvider)
		try {
			const ethersProvider = new BrowserProvider(walletProvider);
			const signer = await ethersProvider.getSigner();

			const planContract = new Contract(tokenAddress, planAbi, signer);
			const claimAction = await planContract.claim();
			await ethersProvider.waitForTransaction(claimAction.hash);

			return true;
		} catch (error) {
				let err = await common.decodeContractError(error);
				throw err;
		}
	}
}
